// ملف script.js - وظائف مساعدة بسيطة

// وظائف للتوافق مع الكود القديم
function addMessageToChat(sender, content) {
    if (window.addMessage) {
        addMessage(sender, content);
    }
}

function toggleVoiceRecording() {
    if (window.startVoiceRecording) {
        startVoiceRecording();
    }
}

function toggleVoiceSettings() {
    if (window.openVoiceSettings) {
        openVoiceSettings();
    }
}

// ربط أحداث الأزرار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔗 ربط أحداث الأزرار...');

    // ربط زر المحادثة الخالصة
    const pureVoiceBtn = document.getElementById('pureVoiceBtn');
    if (pureVoiceBtn) {
        pureVoiceBtn.addEventListener('click', function() {
            console.log('🎤 تم النقر على زر المحادثة الخالصة');
            if (window.togglePureVoiceMode) {
                window.togglePureVoiceMode();
            } else {
                console.error('❌ وظيفة togglePureVoiceMode غير متاحة');
            }
        });
        console.log('✅ تم ربط زر المحادثة الخالصة');
    } else {
        console.error('❌ لم يتم العثور على زر المحادثة الخالصة');
    }

    // ربط زر الصوتي العادي
    const voiceBtn = document.getElementById('voiceBtn');
    if (voiceBtn) {
        voiceBtn.addEventListener('click', function() {
            console.log('🎤 تم النقر على زر الصوتي العادي');
            if (window.toggleVoiceConversation) {
                window.toggleVoiceConversation();
            } else {
                console.error('❌ وظيفة toggleVoiceConversation غير متاحة');
            }
        });
        console.log('✅ تم ربط زر الصوتي العادي');
    }

    // ربط زر Bug Bounty Mode
    const bugBountyBtn = document.getElementById('bugBountyBtn');
    if (bugBountyBtn) {
        bugBountyBtn.addEventListener('click', function() {
            console.log('🔒 تم النقر على زر Bug Bounty Mode');
            if (window.BugBountyCore) {
                // إنشاء مثيل Bug Bounty Mode إذا لم يكن موجوداً
                if (!window.bugBountyInstance) {
                    window.bugBountyInstance = new BugBountyCore();
                }

                // تبديل حالة Bug Bounty Mode
                if (window.bugBountyInstance.isActive) {
                    // إلغاء التفعيل
                    window.bugBountyInstance.deactivate();
                    bugBountyBtn.classList.remove('active');
                    bugBountyBtn.innerHTML = '<i class="fas fa-shield-alt"></i><span>Bug Bounty Mode</span>';
                    bugBountyBtn.title = 'Bug Bounty Mode - فحص الثغرات الأمنية المتقدم';
                } else {
                    // التفعيل
                    window.bugBountyInstance.activate();
                    bugBountyBtn.classList.add('active');
                    bugBountyBtn.innerHTML = '<i class="fas fa-shield-alt"></i><span>إيقاف Bug Bounty</span>';
                    bugBountyBtn.title = 'إيقاف Bug Bounty Mode';
                }
            } else {
                console.error('❌ Bug Bounty Mode غير متاح');
            }
        });
        console.log('✅ تم ربط زر Bug Bounty Mode');
    } else {
        console.error('❌ لم يتم العثور على زر Bug Bounty Mode');
    }

    // ربط زر File Creator
    const fileCreatorBtn = document.getElementById('fileCreatorBtn');
    if (fileCreatorBtn) {
        fileCreatorBtn.addEventListener('click', function() {
            console.log('📁 تم النقر على زر File Creator');
            if (window.FileCreatorCore) {
                // إنشاء مثيل File Creator إذا لم يكن موجوداً
                if (!window.fileCreatorInstance) {
                    window.fileCreatorInstance = new FileCreatorCore();
                }

                // تبديل حالة File Creator
                if (window.fileCreatorInstance.isActive) {
                    // إلغاء التفعيل
                    window.fileCreatorInstance.deactivate();
                    fileCreatorBtn.classList.remove('active');
                    fileCreatorBtn.innerHTML = '<i class="fas fa-file-plus"></i><span>File Creator</span>';
                    fileCreatorBtn.title = 'File Creator - إنشاء ملفات احترافية';
                } else {
                    // التفعيل
                    window.fileCreatorInstance.activate();
                    fileCreatorBtn.classList.add('active');
                    fileCreatorBtn.innerHTML = '<i class="fas fa-file-plus"></i><span>إيقاف File Creator</span>';
                    fileCreatorBtn.title = 'إيقاف File Creator Mode';
                }
            } else {
                console.error('❌ File Creator غير متاح');
            }
        });
        console.log('✅ تم ربط زر File Creator');
    } else {
        console.error('❌ لم يتم العثور على زر File Creator');
    }

    // ربط زر إعدادات الصوت
    const voiceSettingsBtn = document.getElementById('voiceSettingsBtn');
    if (voiceSettingsBtn) {
        voiceSettingsBtn.addEventListener('click', function() {
            console.log('🎤 تم النقر على زر إعدادات الصوت');
            if (window.voiceSettings) {
                window.voiceSettings.show();
            } else {
                console.error('❌ إعدادات الصوت غير متاحة');
            }
        });
        console.log('✅ تم ربط زر إعدادات الصوت');
    } else {
        console.error('❌ لم يتم العثور على زر إعدادات الصوت');
    }

    // ربط زر التحسين الذاتي (في الشريط الجانبي)
    const aiImproveBtn = document.getElementById('aiImproveBtn');
    if (aiImproveBtn) {
        // تأكيد ظهور الزر
        aiImproveBtn.style.display = 'flex';
        aiImproveBtn.style.visibility = 'visible';

        aiImproveBtn.addEventListener('click', function() {
            console.log('🤖 تم النقر على زر التحسين الذاتي (الشريط الجانبي)');
            if (window.aiSelfImprove) {
                toggleAISelfImprovement();
            } else {
                console.error('❌ نظام التحسين الذاتي غير متاح');
            }
        });
        console.log('✅ تم ربط زر التحسين الذاتي في الشريط الجانبي');
    } else {
        console.log('ℹ️ زر التحسين الذاتي في الشريط الجانبي غير موجود');
    }

    // ربط زر التحسين الذاتي (في منطقة الإدخال)
    const aiImproveInputBtn = document.getElementById('aiImproveInputBtn');
    if (aiImproveInputBtn) {
        aiImproveInputBtn.addEventListener('click', function() {
            console.log('🤖 تم النقر على زر التحسين الذاتي (منطقة الإدخال)');
            if (window.aiSelfImprove) {
                toggleAISelfImprovement();
            } else {
                console.error('❌ نظام التحسين الذاتي غير متاح');
            }
        });
        console.log('✅ تم ربط زر التحسين الذاتي في منطقة الإدخال');
    } else {
        console.error('❌ لم يتم العثور على زر التحسين الذاتي في منطقة الإدخال');
    }

    // ربط زر تكوين API
    const apiConfigBtn = document.getElementById('apiConfigBtn');
    if (apiConfigBtn) {
        apiConfigBtn.addEventListener('click', function() {
            console.log('🔌 تم النقر على زر تكوين API');
            if (window.apiConfigInterface) {
                apiConfigInterface.show();
            } else {
                console.error('❌ واجهة تكوين API غير متاحة');
            }
        });
        console.log('✅ تم ربط زر تكوين API');
    } else {
        console.error('❌ لم يتم العثور على زر تكوين API');
    }

    // ربط زر تكوين Hugging Face
    const hfConfigBtn = document.getElementById('hfConfigBtn');
    if (hfConfigBtn) {
        hfConfigBtn.addEventListener('click', function() {
            console.log('🤗 تم النقر على زر تكوين Hugging Face');
            if (window.huggingFaceSettings) {
                huggingFaceSettings.show();
            } else {
                console.error('❌ واجهة تكوين Hugging Face غير متاحة');
            }
        });
        console.log('✅ تم ربط زر تكوين Hugging Face');
    } else {
        console.error('❌ لم يتم العثور على زر تكوين Hugging Face');
    }

    // ربط زر تكوين API (في منطقة الإدخال)
    const apiConfigInputBtn = document.getElementById('apiConfigInputBtn');
    if (apiConfigInputBtn) {
        apiConfigInputBtn.addEventListener('click', function() {
            console.log('🔌 تم النقر على زر تكوين API (منطقة الإدخال)');
            if (window.apiConfigInterface) {
                apiConfigInterface.show();
            } else {
                console.error('❌ واجهة تكوين API غير متاحة');
            }
        });
        console.log('✅ تم ربط زر تكوين API في منطقة الإدخال');
    } else {
        console.error('❌ لم يتم العثور على زر تكوين API في منطقة الإدخال');
    }

    // ربط زر تكوين Hugging Face (في منطقة الإدخال)
    const hfConfigInputBtn = document.getElementById('hfConfigInputBtn');
    if (hfConfigInputBtn) {
        hfConfigInputBtn.addEventListener('click', function() {
            console.log('🤗 تم النقر على زر تكوين Hugging Face (منطقة الإدخال)');
            if (window.huggingFaceSettings) {
                huggingFaceSettings.show();
            } else {
                console.error('❌ واجهة تكوين Hugging Face غير متاحة');
            }
        });
        console.log('✅ تم ربط زر تكوين Hugging Face في منطقة الإدخال');
    } else {
        console.error('❌ لم يتم العثور على زر تكوين Hugging Face في منطقة الإدخال');
    }

    // ربط زر الإرسال
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
        sendBtn.addEventListener('click', function() {
            if (window.sendMessage) {
                window.sendMessage();
            }
        });
    }

    // ربط مدخل النص
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && window.sendMessage) {
                window.sendMessage();
            }
        });
    }

    console.log('✅ تم ربط جميع الأحداث');
});

function closeSettingsModal() {
    if (window.closeSettings) {
        closeSettings();
    }
}

function closeDisplayArea() {
    const displayArea = document.getElementById('displayArea');
    if (displayArea) {
        displayArea.style.display = 'none';
    }
}

// دالة تفعيل/إلغاء تفعيل نظام التحسين الذاتي
function toggleAISelfImprovement() {
    const aiImproveBtn = document.getElementById('aiImproveBtn');
    const aiImproveInputBtn = document.getElementById('aiImproveInputBtn');

    if (!window.aiSelfImprove) {
        console.error('❌ نظام التحسين الذاتي غير متاح');
        return;
    }

    if (window.aiSelfImprove.isActive) {
        // إلغاء التفعيل
        window.aiSelfImprove.deactivate();

        // تحديث الزر في الشريط الجانبي
        if (aiImproveBtn) {
            aiImproveBtn.classList.remove('active');
            aiImproveBtn.innerHTML = '<i class="fas fa-robot"></i><span>التحسين الذاتي</span>';
            aiImproveBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - غير نشط';
        }

        // تحديث الزر في منطقة الإدخال
        if (aiImproveInputBtn) {
            aiImproveInputBtn.classList.remove('active');
            aiImproveInputBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - غير نشط';
        }

        // إشعار صوتي
        if (typeof speakText === 'function') {
            speakText('تم إيقاف نظام التحسين الذاتي', {
                emotion: 'neutral',
                context: 'system'
            });
        }

        console.log('⏹️ تم إيقاف نظام التحسين الذاتي');

    } else {
        // التفعيل
        window.aiSelfImprove.activate();

        // تحديث الزر في الشريط الجانبي
        if (aiImproveBtn) {
            aiImproveBtn.classList.add('active');
            aiImproveBtn.innerHTML = '<i class="fas fa-robot"></i><span>نشط - التحسين الذاتي</span>';
            aiImproveBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - نشط';
        }

        // تحديث الزر في منطقة الإدخال
        if (aiImproveInputBtn) {
            aiImproveInputBtn.classList.add('active');
            aiImproveInputBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - نشط';
        }

        // إشعار صوتي
        if (typeof speakText === 'function') {
            speakText('تم تفعيل نظام التحسين الذاتي. سأراقب الكود وأطلب المساعدة من الذكاء الاصطناعي عند الحاجة', {
                emotion: 'excited',
                context: 'system'
            });
        }

        console.log('🚀 تم تفعيل نظام التحسين الذاتي');

        // عرض واجهة التحسين
        setTimeout(() => {
            window.aiSelfImprove.showInterface();
        }, 1000);
    }
}

// دالة فتح واجهة التحسين الذاتي (للاستخدام المباشر)
function openAISelfImprovement() {
    if (window.aiSelfImprove) {
        window.aiSelfImprove.showInterface();
    } else {
        console.error('❌ نظام التحسين الذاتي غير متاح');
    }
}

// دالة إنشاء زر التحسين الذاتي إذا لم يوجد
function createAIImproveButtonIfMissing() {
    const existingBtn = document.getElementById('aiImproveBtn');
    if (existingBtn) {
        console.log('✅ زر التحسين الذاتي موجود بالفعل');
        return;
    }

    console.log('🔧 إنشاء زر التحسين الذاتي...');

    // البحث عن الحاوية المناسبة
    const toolsContainer = document.querySelector('.tools-container');
    const sidebar = document.querySelector('.sidebar');

    if (toolsContainer || sidebar) {
        const container = toolsContainer || sidebar;

        // إنشاء الزر
        const aiImproveBtn = document.createElement('button');
        aiImproveBtn.className = 'tool-btn ai-improve-btn';
        aiImproveBtn.id = 'aiImproveBtn';
        aiImproveBtn.title = 'التحسين الذاتي بالذكاء الاصطناعي - غير نشط';
        aiImproveBtn.style.display = 'flex';
        aiImproveBtn.innerHTML = `
            <i class="fas fa-robot"></i>
            <span>التحسين الذاتي</span>
        `;

        // إضافة الزر للحاوية
        container.appendChild(aiImproveBtn);

        // ربط الأحداث
        aiImproveBtn.addEventListener('click', function() {
            console.log('🤖 تم النقر على زر التحسين الذاتي (المنشأ ديناميكياً)');
            if (window.aiSelfImprove) {
                toggleAISelfImprovement();
            } else {
                console.error('❌ نظام التحسين الذاتي غير متاح');
            }
        });

        console.log('✅ تم إنشاء زر التحسين الذاتي بنجاح');
    } else {
        console.error('❌ لم يتم العثور على حاوية مناسبة لإضافة الزر');
    }
}

// تصدير الدوال للاستخدام العام
window.toggleAISelfImprovement = toggleAISelfImprovement;
window.openAISelfImprovement = openAISelfImprovement;
window.createAIImproveButtonIfMissing = createAIImproveButtonIfMissing;

// تصدير للتوافق
window.addMessageToChat = addMessageToChat;
